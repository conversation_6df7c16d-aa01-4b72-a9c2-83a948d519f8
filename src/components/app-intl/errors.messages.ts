import { defineMessages } from 'react-intl';

export const errorMessages = defineMessages({
  // Authentication errors
  'errors.auth.unauthenticated': {
    id: 'errors.auth.unauthenticated',
    defaultMessage: 'Authentication required.',
  },
  'errors.auth.permissionDenied': {
    id: 'errors.auth.permissionDenied',
    defaultMessage: 'Permission denied.',
  },
  'errors.auth.permissionDeniedWithOperation': {
    id: 'errors.auth.permissionDeniedWithOperation',
    defaultMessage: 'You can only perform {operation} for yourself.',
  },
  'errors.auth.adminOnly': {
    id: 'errors.auth.adminOnly',
    defaultMessage: 'Only admin users can perform this operation.',
  },
  'errors.auth.userNotFound': {
    id: 'errors.auth.userNotFound',
    defaultMessage: 'User not found.',
  },
  'errors.auth.tonWalletRequired': {
    id: 'errors.auth.tonWalletRequired',
    defaultMessage: 'User does not have a TON wallet address configured.',
  },

  // Validation errors
  'errors.validation.requiredField': {
    id: 'errors.validation.requiredField',
    defaultMessage: '{field} is required.',
  },
  'errors.validation.positiveAmountRequired': {
    id: 'errors.validation.positiveAmountRequired',
    defaultMessage: '{fieldName} must be greater than 0.',
  },
  'errors.validation.invalidOrderId': {
    id: 'errors.validation.invalidOrderId',
    defaultMessage: 'Valid order ID is required.',
  },
  'errors.validation.invalidCollectionId': {
    id: 'errors.validation.invalidCollectionId',
    defaultMessage: 'Valid collection ID is required.',
  },
  'errors.validation.invalidPrice': {
    id: 'errors.validation.invalidPrice',
    defaultMessage: 'Valid price is required.',
  },
  'errors.validation.invalidSecondaryMarketPrice': {
    id: 'errors.validation.invalidSecondaryMarketPrice',
    defaultMessage: 'Valid secondary market price is required.',
  },

  // Order errors
  'errors.order.orderNotFound': {
    id: 'errors.order.orderNotFound',
    defaultMessage: 'Order not found.',
  },
  'errors.order.insufficientBalance': {
    id: 'errors.order.insufficientBalance',
    defaultMessage: 'Insufficient balance.',
  },
  'errors.order.collectionNotFound': {
    id: 'errors.order.collectionNotFound',
    defaultMessage: 'Collection not found.',
  },
  'errors.order.collectionNotActive': {
    id: 'errors.order.collectionNotActive',
    defaultMessage: 'Collection is not active.',
  },
  'errors.order.onlyPaidOrdersSecondaryMarket': {
    id: 'errors.order.onlyPaidOrdersSecondaryMarket',
    defaultMessage:
      'Only orders with PAID status can be listed on secondary market.',
  },
  'errors.order.onlyBuyerCanSetSecondaryPrice': {
    id: 'errors.order.onlyBuyerCanSetSecondaryPrice',
    defaultMessage: 'Only the current buyer can set secondary market price.',
  },
  'errors.order.orderMustHaveBuyerAndSeller': {
    id: 'errors.order.orderMustHaveBuyerAndSeller',
    defaultMessage:
      'Order must have both buyer and seller to be listed on secondary market.',
  },
  'errors.order.secondaryPriceExceedsCollateral': {
    id: 'errors.order.secondaryPriceExceedsCollateral',
    defaultMessage:
      'Secondary market price cannot exceed total collateral of {totalCollateral} TON (buyer: {buyerAmount} TON + seller: {sellerAmount} TON).',
  },
  'errors.order.orderNotAvailableSecondaryMarket': {
    id: 'errors.order.orderNotAvailableSecondaryMarket',
    defaultMessage: 'Order is not available on secondary market.',
  },
  'errors.order.onlyPaidOrdersPurchasable': {
    id: 'errors.order.onlyPaidOrdersPurchasable',
    defaultMessage:
      'Only orders with PAID status can be purchased on secondary market.',
  },
  'errors.order.sellerCannotPurchaseOwnOrder': {
    id: 'errors.order.sellerCannotPurchaseOwnOrder',
    defaultMessage:
      'Seller cannot purchase their own order on secondary market.',
  },
  'errors.order.buyerCannotPurchaseSameOrder': {
    id: 'errors.order.buyerCannotPurchaseSameOrder',
    defaultMessage: 'You cannot purchase the same order again.',
  },
  'errors.order.secondaryPriceBelowMinimum': {
    id: 'errors.order.secondaryPriceBelowMinimum',
    defaultMessage: 'Secondary market price must be at least {minPrice} TON.',
  },

  // Withdrawal errors
  'errors.withdrawal.amountBelowMinimum': {
    id: 'errors.withdrawal.amountBelowMinimum',
    defaultMessage: 'Withdrawal amount must be at least {minAmount} TON.',
  },
  'errors.withdrawal.amountAboveMaximum': {
    id: 'errors.withdrawal.amountAboveMaximum',
    defaultMessage: 'Withdrawal amount cannot exceed {maxAmount} TON.',
  },
  'errors.withdrawal.insufficientAvailableBalance': {
    id: 'errors.withdrawal.insufficientAvailableBalance',
    defaultMessage: 'Insufficient available balance for withdrawal.',
  },
  'errors.withdrawal.amountTooSmallAfterFees': {
    id: 'errors.withdrawal.amountTooSmallAfterFees',
    defaultMessage: 'Amount too small after fees.',
  },

  // Generic errors
  'errors.generic.serverError': {
    id: 'errors.generic.serverError',
    defaultMessage: 'Server error occurred.',
  },
  'errors.generic.unknownError': {
    id: 'errors.generic.unknownError',
    defaultMessage: 'An unknown error occurred.',
  },
  'errors.generic.operationFailed': {
    id: 'errors.generic.operationFailed',
    defaultMessage: 'Operation failed. Please try again.',
  },
});

{"actions.apply": "Apply", "actions.cancel": "Cancel", "actions.confirm": "Confirm", "actions.delete": "Delete", "actions.edit": "Edit", "actions.pause": "Pause", "actions.reject": "Reject", "actions.resend": "Resend", "actions.save": "Save", "actions.saved": "Saved", "actions.send": "Send", "actions.sent": "<PERSON><PERSON>", "actions.signIn": "Sign in", "actions.signOut": "Sign out", "actions.signUp": "Sign up", "actions.submit": "Submit", "mock.message": "Mock message", "nouns.confirmation": "Confirmation", "nouns.description": "Description", "nouns.email": "Email", "nouns.error": "Error", "nouns.from": "From", "nouns.name": "Name", "nouns.password": "Password", "nouns.price": "Price", "nouns.service": "Service", "errors.auth.unauthenticated": "Authentication required.", "errors.auth.permissionDenied": "Permission denied.", "errors.auth.permissionDeniedWithOperation": "You can only perform {operation} for yourself.", "errors.auth.adminOnly": "Only admin users can perform this operation.", "errors.auth.userNotFound": "User not found.", "errors.auth.tonWalletRequired": "User does not have a TON wallet address configured.", "errors.validation.requiredField": "{field} is required.", "errors.validation.positiveAmountRequired": "{fieldName} must be greater than 0.", "errors.validation.invalidOrderId": "Valid order ID is required.", "errors.validation.invalidCollectionId": "Valid collection ID is required.", "errors.validation.invalidPrice": "Valid price is required.", "errors.validation.invalidSecondaryMarketPrice": "Valid secondary market price is required.", "errors.order.orderNotFound": "Order not found.", "errors.order.insufficientBalance": "Insufficient balance.", "errors.order.collectionNotFound": "Collection not found.", "errors.order.collectionNotActive": "Collection is not active.", "errors.order.onlyPaidOrdersSecondaryMarket": "Only orders with PAID status can be listed on secondary market.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Only the current buyer can set secondary market price.", "errors.order.orderMustHaveBuyerAndSeller": "Order must have both buyer and seller to be listed on secondary market.", "errors.order.secondaryPriceExceedsCollateral": "Secondary market price cannot exceed total collateral of {totalCollateral} TON (buyer: {buyerAmount} TON + seller: {sellerAmount} TON).", "errors.order.orderNotAvailableSecondaryMarket": "Order is not available on secondary market.", "errors.order.onlyPaidOrdersPurchasable": "Only orders with PAID status can be purchased on secondary market.", "errors.order.sellerCannotPurchaseOwnOrder": "Seller cannot purchase their own order on secondary market.", "errors.order.buyerCannotPurchaseSameOrder": "You cannot purchase the same order again.", "errors.withdrawal.amountBelowMinimum": "Withdrawal amount must be at least {minAmount} TON.", "errors.withdrawal.amountAboveMaximum": "Withdrawal amount cannot exceed {maxAmount} TON.", "errors.withdrawal.insufficientAvailableBalance": "Insufficient available balance for withdrawal.", "errors.withdrawal.amountTooSmallAfterFees": "Amount too small after fees.", "errors.generic.serverError": "Server error occurred.", "errors.generic.unknownError": "An unknown error occurred.", "errors.generic.operationFailed": "Operation failed. Please try again."}
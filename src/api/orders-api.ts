import type {
  DocumentSnapshot,
} from 'firebase/firestore';
import {
  and,
  collection,
  getDocs,
  limit,
  or,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  AppCloudFunctions,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from '@/constants/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export type OrderType = 'buyers' | 'sellers';





const isValidSecondaryOrder = (order: OrderEntity): boolean => {
  return (
    order.status === 'paid' &&
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};

const applyPriceFilter = (
  order: OrderEntity,
  filters: OrderFilters,
): boolean => {
  const isRegularOrder =
    order.status === 'active' && !order.secondaryMarketPrice;
  const price = isRegularOrder
    ? order.price
    : (order.secondaryMarketPrice ?? 0);

  if (filters.minPrice && price < filters.minPrice) return false;
  if (filters.maxPrice && price > filters.maxPrice) return false;

  return true;
};

const sortUnifiedOrders = (
  orders: OrderEntity[],
  sortBy?: string,
): OrderEntity[] => {
  if (!sortBy) {
    return orders.sort((a, b) => {
      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return bDate - aDate; // Default: newest first
    });
  }

  return orders.sort((a, b) => {
    if (sortBy.includes('price')) {
      const aPrice = isValidSecondaryOrder(a)
        ? (a.secondaryMarketPrice ?? 0)
        : a.price;
      const bPrice = isValidSecondaryOrder(b)
        ? (b.secondaryMarketPrice ?? 0)
        : b.price;

      return sortBy.includes('asc') ? aPrice - bPrice : bPrice - aPrice;
    } else {
      // Sort by date
      const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return sortBy.includes('asc') ? aDate - bDate : bDate - aDate;
    }
  });
};

export const getUnifiedBuyersOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        // Primary market orders: active status, has seller, no secondary price
        where('status', '==', 'active'),
        // Secondary market orders: paid status, has secondary price > 0
        and(
          where('status', '==', 'paid'),
          where('secondaryMarketPrice', '>', 0),
        ),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // For compound queries with different price fields, we can only sort by common fields
    // We'll sort by createdAt and handle price sorting client-side
    const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';
    q = query(q, orderBy('createdAt', sortDirection));

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const ordersToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    let orders: OrderEntity[] = ordersToReturn
      .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
      .filter((order) => {
        const isRegularOrder =
          order.status === 'active' &&
          order.sellerId &&
          !order.buyerId &&
          !order.secondaryMarketPrice;
        const isSecondaryOrder = isValidSecondaryOrder(order);

        if (isRegularOrder) {
          return (
            (!filters.currentUserId ||
              order.sellerId !== filters.currentUserId) &&
            applyPriceFilter(order, filters)
          );
        } else if (isSecondaryOrder) {
          return applyPriceFilter(order, filters);
        }

        return false;
      });

    // Apply client-side sorting if price sorting is requested
    if (filters.sortBy?.includes('price')) {
      orders = sortUnifiedOrders(orders, filters.sortBy);
    }

    const lastDoc = hasMore ? ordersToReturn[ordersToReturn.length - 1] : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching unified buyers orders:', error);
    throw error;
  }
};

export const getUnifiedSellerOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        // Primary market orders: active status, has buyer, no secondary price
        where('status', '==', 'active'),
        // Secondary market orders: paid status, has secondary price > 0
        and(
          where('status', '==', 'paid'),
          where('secondaryMarketPrice', '>', 0),
        ),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // For compound queries with different price fields, we can only sort by common fields
    // We'll sort by createdAt and handle price sorting client-side
    const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';
    q = query(q, orderBy('createdAt', sortDirection));

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const ordersToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    let orders: OrderEntity[] = ordersToReturn
    .map((doc) => ({ id: doc.id, ...doc.data() }) as OrderEntity)
      .filter((order) => {
        const isRegularOrder =
          order.status === 'active' &&
          order.buyerId &&
          !order.sellerId &&
          !order.secondaryMarketPrice;
        const isSecondaryOrder = isValidSecondaryOrder(order);

        if (isRegularOrder) {
          return (
            (!filters.currentUserId ||
              order.buyerId !== filters.currentUserId) &&
            applyPriceFilter(order, filters)
          );
        } else if (isSecondaryOrder) {
          return applyPriceFilter(order, filters);
        }

        return false;
      });

    // Apply client-side sorting if price sorting is requested
    if (filters.sortBy?.includes('price')) {
      orders = sortUnifiedOrders(orders, filters.sortBy);
    }

    const lastDoc = hasMore ? ordersToReturn[ordersToReturn.length - 1] : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching unified seller orders:', error);
    throw error;
  }
};

// Replace the old functions with the new unified implementations
export const getOrdersForBuyers = getUnifiedBuyersOrders;
export const getOrdersForSellers = getUnifiedSellerOrders;

export const makePurchaseAsBuyer = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsBuyer);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsSeller);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};

export const makeSecondaryMarketPurchase = async (orderId: string) => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makeSecondaryMarketPurchase);

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    return result.data;
  } catch (error) {
    console.error('Error making secondary market purchase:', error);
    throw error;
  }
};

export const getActivityOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;

    // Build query for activity orders (paid, gift_sent_to_relayer, fulfilled)
    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      or(
        where('status', '==', OrderStatus.PAID),
        where('status', '==', OrderStatus.GIFT_SENT_TO_RELAYER),
        where('status', '==', OrderStatus.FULFILLED),
      ),
    );

    // Add collection filter if specified
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Apply sorting - use 'amount' field from database
    if (filters.sortBy === 'price_desc') {
      q = query(q, orderBy('amount', 'desc'));
    } else if (filters.sortBy === 'price_asc') {
      q = query(q, orderBy('amount', 'asc'));
    } else {
      // Default to date descending (newest first)
      q = query(q, orderBy('updatedAt', 'desc'));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1));

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const ordersToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    // Transform documents and apply price filtering
    let orders: OrderEntity[] = ordersToReturn
      .map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Map 'amount' field from database to 'price' field expected by frontend
          price: data.amount ?? data.price ?? 0,
        } as OrderEntity;
      })
      .filter((order) => applyPriceFilter(order, filters));

    // Apply client-side sorting if price sorting is requested
    if (filters.sortBy?.includes('price')) {
      orders = sortUnifiedOrders(orders, filters.sortBy);
    }

    const lastDoc = hasMore ? ordersToReturn[ordersToReturn.length - 1] : null;

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error('Error fetching activity orders:', error);
    throw error;
  }
};
